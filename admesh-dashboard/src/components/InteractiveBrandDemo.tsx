"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  Globe,
  MessageSquare,
  Layout,
  Sparkles,
  ExternalLink,
  Star,
  CheckCircle,
  Building
} from "lucide-react";

interface AdUnitType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  preview: React.ReactNode;
}

interface MockProduct {
  title: string;
  description: string;
  pricing: string;
  features: string[];
  rating: number;
  category: string;
}

export default function InteractiveBrandDemo() {
  const [websiteUrl, setWebsiteUrl] = useState("");
  const [selectedAdUnit, setSelectedAdUnit] = useState<string>("citation");
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  // Mock product data based on URL
  const getMockProduct = (url: string): MockProduct => {
    const domain = url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
    
    // Simple domain-based mock data
    if (domain.includes('shopify') || domain.includes('ecommerce')) {
      return {
        title: "E-commerce Platform",
        description: "Complete online store solution with payment processing",
        pricing: "Starting at $29/month",
        features: ["Payment Processing", "Inventory Management", "SEO Tools"],
        rating: 4.8,
        category: "E-commerce"
      };
    } else if (domain.includes('saas') || domain.includes('software')) {
      return {
        title: "SaaS Solution",
        description: "Cloud-based software for business automation",
        pricing: "Starting at $49/month",
        features: ["Cloud Storage", "API Access", "24/7 Support"],
        rating: 4.6,
        category: "Software"
      };
    } else {
      return {
        title: domain || "Your Brand",
        description: "Professional business solution tailored for your needs",
        pricing: "Custom pricing available",
        features: ["Professional Service", "Expert Support", "Custom Solutions"],
        rating: 4.7,
        category: "Business Services"
      };
    }
  };

  const adUnitTypes: AdUnitType[] = [
    {
      id: "citation",
      name: "Citation Style",
      description: "Numbered citations within natural conversation flow",
      icon: <MessageSquare className="w-5 h-5" />,
      preview: (
        <div className="bg-gray-50 p-3 rounded-lg text-sm max-w-sm mx-auto">
          <p className="text-gray-700 mb-2 text-xs">
            For growing businesses, I recommend checking out professional solutions like{" "}
            <span className="inline-flex items-center">
              <span className="text-blue-600 cursor-pointer hover:underline">
                {getMockProduct(websiteUrl).title}
              </span>
              <sup className="ml-1 w-3 h-3 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs">
                1
              </sup>
            </span>
            {" "}which offers comprehensive features for scaling operations.
          </p>
          <div className="border-t pt-2 mt-2">
            <div className="flex items-start gap-2">
              <span className="w-4 h-4 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium">
                1
              </span>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 text-xs">{getMockProduct(websiteUrl).title}</h4>
                <p className="text-gray-600 text-xs mt-1">{getMockProduct(websiteUrl).description}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-xs px-1 py-0">{getMockProduct(websiteUrl).category}</Badge>
                  <span className="text-xs text-gray-500">{getMockProduct(websiteUrl).pricing}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "inline",
      name: "Inline Cards",
      description: "Compact recommendation cards within chat interfaces",
      icon: <Layout className="w-5 h-5" />,
      preview: (
        <div className="bg-gray-50 p-3 rounded-lg max-w-sm mx-auto">
          <div className="bg-white border rounded-lg p-2 shadow-sm">
            <div className="flex items-start gap-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Building className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 text-xs">{getMockProduct(websiteUrl).title}</h4>
                <p className="text-gray-600 text-xs mt-1 line-clamp-2">{getMockProduct(websiteUrl).description}</p>
                <div className="flex items-center gap-2 mt-1">
                  <div className="flex items-center gap-1">
                    <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).rating}</span>
                  </div>
                  <span className="text-xs text-gray-500">•</span>
                  <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).pricing}</span>
                </div>
                <Button size="sm" className="mt-1 h-6 text-xs">
                  Learn More <ExternalLink className="w-2 h-2 ml-1" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "floating",
      name: "Floating Widget",
      description: "Non-intrusive floating recommendations",
      icon: <Sparkles className="w-5 h-5" />,
      preview: (
        <div className="bg-gray-50 p-3 rounded-lg relative max-w-sm mx-auto h-24">
          <div className="text-gray-400 text-xs mb-2">Chat conversation continues here...</div>
          <div className="absolute bottom-2 right-2 bg-white border shadow-lg rounded-lg p-2 max-w-xs">
            <div className="flex items-center gap-1 mb-1">
              <Sparkles className="w-3 h-3 text-blue-600" />
              <span className="text-xs font-medium text-gray-700">Recommended</span>
            </div>
            <h4 className="font-medium text-gray-900 text-xs">{getMockProduct(websiteUrl).title}</h4>
            <p className="text-gray-600 text-xs">{getMockProduct(websiteUrl).pricing}</p>
            <Button size="sm" className="mt-1 w-full h-5 text-xs">
              View Details
            </Button>
          </div>
        </div>
      )
    }
  ];

  const handlePreview = () => {
    if (websiteUrl.trim()) {
      setIsPreviewMode(true);
    }
  };

  const selectedAdUnitData = adUnitTypes.find(unit => unit.id === selectedAdUnit);

  return (
    <div className="w-full max-w-lg mx-auto px-4 sm:px-0">
      <Card className="border border-gray-200 shadow-lg">
        <CardHeader className="text-center pb-4">
          <CardTitle className="flex items-center justify-center gap-2 text-lg">
            <Globe className="w-5 h-5 text-blue-600" />
            See Your Brand in Action
          </CardTitle>
          <p className="text-gray-600 text-sm">
            Enter your website and see how your brand appears in different ad formats
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* URL Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Your Website URL</label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                placeholder="https://yourbrand.com"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className="flex-1 text-sm"
              />
              <Button
                onClick={handlePreview}
                disabled={!websiteUrl.trim()}
                className="bg-blue-600 hover:bg-blue-700 sm:w-auto w-full text-sm"
                size="sm"
              >
                Preview
              </Button>
            </div>
          </div>

          {/* Ad Unit Type Selector */}
          {websiteUrl.trim() && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-2"
            >
              <label className="text-sm font-medium text-gray-700">Choose Ad Unit Format</label>
              <div className="grid grid-cols-1 gap-2">
                {adUnitTypes.map((unit) => (
                  <div
                    key={unit.id}
                    className={`border rounded-lg p-2 cursor-pointer transition-all ${
                      selectedAdUnit === unit.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedAdUnit(unit.id)}
                  >
                    <div className="flex items-center gap-2">
                      <div className={`w-6 h-6 rounded flex items-center justify-center ${
                        selectedAdUnit === unit.id ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'
                      }`}>
                        {unit.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 text-sm">{unit.name}</h4>
                        <p className="text-gray-600 text-xs">{unit.description}</p>
                      </div>
                      {selectedAdUnit === unit.id && (
                        <CheckCircle className="w-4 h-4 text-blue-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Preview */}
          {isPreviewMode && selectedAdUnitData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
            >
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Live Preview</h4>
                {selectedAdUnitData.preview}
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                <div className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="w-3 h-3" />
                  <span className="text-sm font-medium">Ready to integrate!</span>
                </div>
                <p className="text-green-600 text-xs mt-1">
                  Your brand will appear naturally in AI conversations when users search for {getMockProduct(websiteUrl).category.toLowerCase()} solutions.
                </p>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
