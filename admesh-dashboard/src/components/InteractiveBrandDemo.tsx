"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { motion } from "framer-motion";
import {
  Globe,
  MessageSquare,
  Sparkles,
  Star,
  Building,
  User,
  Bot,
  FileText,
  PenTool,
  Send
} from "lucide-react";

interface AdUnitType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  preview: React.ReactNode;
}

interface ExampleWebsite {
  name: string;
  url: string;
  category: string;
  description: string;
}

interface MockProduct {
  title: string;
  description: string;
  pricing: string;
  features: string[];
  rating: number;
  category: string;
}

export default function InteractiveBrandDemo() {
  const [websiteUrl, setWebsiteUrl] = useState("");
  const [selectedAdUnit, setSelectedAdUnit] = useState<string>("chat");
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const exampleWebsites: ExampleWebsite[] = [
    {
      name: "Shopify",
      url: "https://shopify.com",
      category: "E-commerce Platform",
      description: "Complete e-commerce solution for online stores"
    },
    {
      name: "Notion",
      url: "https://notion.so",
      category: "Productivity Tool",
      description: "All-in-one workspace for notes, docs, and collaboration"
    },
    {
      name: "Stripe",
      url: "https://stripe.com",
      category: "Payment Processing",
      description: "Online payment infrastructure for businesses"
    }
  ];

  // Mock product data based on URL
  const getMockProduct = (url: string): MockProduct => {
    const domain = url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
    
    // Simple domain-based mock data
    if (domain.includes('shopify') || domain.includes('ecommerce')) {
      return {
        title: "E-commerce Platform",
        description: "Complete online store solution with payment processing",
        pricing: "Starting at $29/month",
        features: ["Payment Processing", "Inventory Management", "SEO Tools"],
        rating: 4.8,
        category: "E-commerce"
      };
    } else if (domain.includes('saas') || domain.includes('software')) {
      return {
        title: "SaaS Solution",
        description: "Cloud-based software for business automation",
        pricing: "Starting at $49/month",
        features: ["Cloud Storage", "API Access", "24/7 Support"],
        rating: 4.6,
        category: "Software"
      };
    } else {
      return {
        title: domain || "Your Brand",
        description: "Professional business solution tailored for your needs",
        pricing: "Custom pricing available",
        features: ["Professional Service", "Expert Support", "Custom Solutions"],
        rating: 4.7,
        category: "Business Services"
      };
    }
  };

  const adUnitTypes: AdUnitType[] = [
    {
      id: "chat",
      name: "Chat Application",
      description: "Customer support and messaging platforms",
      icon: <MessageSquare className="w-5 h-5" />,
      preview: (
        <div className="bg-white border rounded-lg p-3 max-w-sm mx-auto shadow-sm">
          {/* Chat Header */}
          <div className="flex items-center gap-2 pb-2 border-b">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <Bot className="w-3 h-3 text-blue-600" />
            </div>
            <div className="flex-1">
              <h4 className="text-xs font-medium">Support Assistant</h4>
              <div className="flex items-center gap-1">
                <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                <span className="text-xs text-gray-500">Online</span>
              </div>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="space-y-2 py-2">
            <div className="flex gap-2">
              <User className="w-4 h-4 text-gray-400 mt-1" />
              <div className="bg-gray-100 rounded-lg p-2 text-xs">
                I need a payment solution for my online store
              </div>
            </div>

            <div className="flex gap-2">
              <Bot className="w-4 h-4 text-blue-600 mt-1" />
              <div className="bg-blue-50 rounded-lg p-2 text-xs">
                I'd recommend {getMockProduct(websiteUrl).title} for payment processing. It's trusted by millions of businesses worldwide.

                {/* Integrated Recommendation */}
                <div className="mt-2 bg-white border rounded p-2 shadow-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                      <Building className="w-3 h-3 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900 text-xs">{getMockProduct(websiteUrl).title}</h5>
                      <p className="text-gray-600 text-xs">{getMockProduct(websiteUrl).description}</p>
                      <div className="flex items-center gap-1 mt-1">
                        <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).rating}</span>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).pricing}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Input */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <input className="flex-1 text-xs bg-gray-50 rounded px-2 py-1" placeholder="Type a message..." />
            <Send className="w-3 h-3 text-gray-400" />
          </div>
        </div>
      )
    },
    {
      id: "notes",
      name: "AI Note-Taking App",
      description: "Recommendations after AI summaries in productivity tools",
      icon: <FileText className="w-5 h-5" />,
      preview: (
        <div className="bg-white border rounded-lg p-3 max-w-sm mx-auto shadow-sm">
          {/* Note-taking App Header */}
          <div className="flex items-center gap-2 pb-2 border-b">
            <FileText className="w-4 h-4 text-purple-600" />
            <span className="text-xs font-medium">Meeting Notes - Q4 Planning</span>
            <div className="ml-auto text-xs text-gray-500">2:34 PM</div>
          </div>

          {/* Note Content */}
          <div className="py-2 space-y-2">
            <div className="text-xs text-gray-700">
              <strong>Key Discussion Points:</strong>
              <ul className="mt-1 space-y-1 text-xs">
                <li>• Need to implement new payment system</li>
                <li>• Looking for e-commerce platform migration</li>
                <li>• Budget allocated for productivity tools</li>
              </ul>
            </div>

            {/* AI Summary */}
            <div className="bg-purple-50 border border-purple-200 rounded p-2">
              <div className="flex items-center gap-1 mb-1">
                <Sparkles className="w-3 h-3 text-purple-600" />
                <span className="text-xs font-medium text-purple-700">AI Summary</span>
              </div>
              <p className="text-xs text-gray-700">
                Team discussed implementing {getMockProduct(websiteUrl).title} for their upcoming project requirements.
              </p>

              {/* Integrated Recommendation */}
              <div className="mt-2 bg-white border rounded p-2">
                <div className="flex items-center gap-1 mb-1">
                  <Building className="w-3 h-3 text-blue-600" />
                  <span className="text-xs font-medium text-gray-700">Recommended Solution</span>
                </div>
                <div className="flex items-start gap-2">
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900 text-xs">{getMockProduct(websiteUrl).title}</h5>
                    <p className="text-gray-600 text-xs">{getMockProduct(websiteUrl).description}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).rating}</span>
                      <span className="text-xs text-gray-500">•</span>
                      <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).pricing}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Note Actions */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <Button size="sm" variant="outline" className="text-xs h-6">Share</Button>
            <Button size="sm" variant="outline" className="text-xs h-6">Export</Button>
          </div>
        </div>
      )
    },
    {
      id: "writing",
      name: "AI Writing Assistant",
      description: "Contextual suggestions in writing and content tools",
      icon: <PenTool className="w-5 h-5" />,
      preview: (
        <div className="bg-white border rounded-lg p-3 max-w-sm mx-auto shadow-sm">
          {/* Writing App Header */}
          <div className="flex items-center gap-2 pb-2 border-b">
            <PenTool className="w-4 h-4 text-green-600" />
            <span className="text-xs font-medium">Blog Post Draft</span>
            <div className="ml-auto flex items-center gap-1">
              <div className="w-1 h-1 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-500">Auto-save</span>
            </div>
          </div>

          {/* Writing Content */}
          <div className="py-2 space-y-2">
            <div className="text-xs text-gray-700">
              <h4 className="font-medium mb-1">How to Choose the Right Payment Processor</h4>
              <p className="text-xs leading-relaxed">
                When selecting a payment processor for your business, consider factors like transaction fees, security features, and integration capabilities. Many successful businesses rely on...
              </p>
            </div>

            {/* AI Writing Suggestion */}
            <div className="bg-green-50 border border-green-200 rounded p-2">
              <div className="flex items-center gap-1 mb-1">
                <Sparkles className="w-3 h-3 text-green-600" />
                <span className="text-xs font-medium text-green-700">AI Suggestion</span>
              </div>
              <p className="text-xs text-gray-700 mb-2">
                Consider mentioning {getMockProduct(websiteUrl).title} as a leading solution in this space.
              </p>

              {/* Integrated Recommendation */}
              <div className="bg-white border rounded p-2">
                <div className="flex items-start gap-2">
                  <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                    <Building className="w-3 h-3 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900 text-xs">{getMockProduct(websiteUrl).title}</h5>
                    <p className="text-gray-600 text-xs">{getMockProduct(websiteUrl).description}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).rating}</span>
                      <span className="text-xs text-gray-500">•</span>
                      <span className="text-xs text-gray-600">{getMockProduct(websiteUrl).pricing}</span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-1 mt-2">
                  <Button size="sm" variant="outline" className="text-xs h-5 px-2">Insert</Button>
                  <Button size="sm" variant="outline" className="text-xs h-5 px-2">Learn More</Button>
                </div>
              </div>
            </div>
          </div>

          {/* Writing Tools */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <span className="text-xs text-gray-500">245 words</span>
            <div className="ml-auto flex gap-1">
              <Button size="sm" variant="outline" className="text-xs h-6">Save</Button>
              <Button size="sm" className="text-xs h-6">Publish</Button>
            </div>
          </div>
        </div>
      )
    }
  ];

  const handlePreview = () => {
    if (websiteUrl.trim()) {
      setIsPreviewMode(true);
    }
  };

  const selectedAdUnitData = adUnitTypes.find(unit => unit.id === selectedAdUnit);

  return (
    <div className="w-full max-w-lg mx-auto px-4 sm:px-0">
      <Card className="border border-gray-200 shadow-lg">
        <CardHeader className="text-center pb-4">
          <CardTitle className="flex items-center justify-center gap-2 text-lg">
            <Globe className="w-5 h-5 text-blue-600" />
            See Your Brand in Action
          </CardTitle>
          <p className="text-gray-600 text-sm">
            Enter your website and see how your brand appears in different ad formats
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* URL Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Your Website URL</label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                placeholder="https://yourbrand.com"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className="flex-1 text-sm"
              />
              <Button
                onClick={handlePreview}
                disabled={!websiteUrl.trim()}
                className="bg-blue-600 hover:bg-blue-700 sm:w-auto w-full text-sm"
                size="sm"
              >
                Preview
              </Button>
            </div>

            {/* Quick Examples */}
            <div className="space-y-1">
              <span className="text-xs text-gray-500">Try these examples:</span>
              <div className="flex flex-wrap gap-1">
                {exampleWebsites.map((example) => (
                  <Button
                    key={example.name}
                    variant="outline"
                    size="sm"
                    className="text-xs h-6 px-2"
                    onClick={() => {
                      setWebsiteUrl(example.url);
                      setIsPreviewMode(true);
                    }}
                  >
                    {example.name}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Ad Unit Type Selector */}
          {websiteUrl.trim() && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-2"
            >
              <label className="text-sm font-medium text-gray-700">Choose Ad Unit Format</label>
              <Select value={selectedAdUnit} onValueChange={setSelectedAdUnit}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select an ad unit format" />
                </SelectTrigger>
                <SelectContent>
                  {adUnitTypes.map((unit) => (
                    <SelectItem key={unit.id} value={unit.id}>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 flex items-center justify-center">
                          {unit.icon}
                        </div>
                        <div>
                          <span className="font-medium">{unit.name}</span>
                          <span className="text-xs text-gray-500 ml-2">- {unit.description}</span>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </motion.div>
          )}

          {/* Preview */}
          {isPreviewMode && selectedAdUnitData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
            >
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Live Preview</h4>
                {selectedAdUnitData.preview}
              </div>

            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
