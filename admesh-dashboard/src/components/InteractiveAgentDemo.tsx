"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  Bot,
  MessageSquare,
  Mic,
  Monitor,
  CheckCircle,
  Star,
  Building,
  Sparkles,
  User,
  Volume2
} from "lucide-react";

interface AgentScenario {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  preview: React.ReactNode;
}

export default function InteractiveAgentDemo() {
  const [selectedScenario, setSelectedScenario] = useState<string>("chat");

  const agentScenarios: AgentScenario[] = [
    {
      id: "chat",
      name: "Chat Assistant",
      description: "Natural recommendations within conversation flow",
      icon: <MessageSquare className="w-5 h-5" />,
      preview: (
        <div className="bg-white border rounded-lg p-3 max-w-sm mx-auto">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
              <Bot className="w-3 h-3 text-purple-600" />
            </div>
            <span className="font-medium text-gray-900 text-sm">AI Assistant</span>
            <div className="ml-auto w-2 h-2 bg-green-500 rounded-full"></div>
          </div>

          <div className="space-y-2">
            <div className="bg-gray-100 p-2 rounded-lg text-xs">
              <User className="w-3 h-3 inline mr-1 text-gray-600" />
              I need a CRM for my startup
            </div>

            <div className="bg-purple-50 p-2 rounded-lg text-xs">
              <Bot className="w-3 h-3 inline mr-1 text-purple-600" />
              Here are some great CRM options:

              <div className="mt-2 bg-white border rounded p-2 shadow-sm">
                <div className="flex items-start gap-2">
                  <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                    <Building className="w-3 h-3 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 text-xs">HubSpot CRM</h4>
                    <p className="text-gray-600 text-xs">Free CRM with powerful features</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-2 h-2 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-gray-600">4.5</span>
                      <Badge variant="secondary" className="text-xs px-1 py-0">Free</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-2 text-xs text-gray-500 flex items-center gap-1">
            <Sparkles className="w-2 h-2" />
            Powered by AdMesh
          </div>
        </div>
      )
    },
    {
      id: "voice",
      name: "Voice Assistant",
      description: "Seamless recommendations in voice interactions",
      icon: <Mic className="w-5 h-5" />,
      preview: (
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 border rounded-lg p-3 max-w-sm mx-auto">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <Volume2 className="w-3 h-3 text-blue-600" />
            </div>
            <span className="font-medium text-gray-900 text-sm">Voice AI</span>
            <div className="ml-auto flex items-center gap-1">
              <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="bg-white/70 p-2 rounded-lg text-xs">
              <User className="w-3 h-3 inline mr-1 text-gray-600" />
              "What's the best email marketing tool?"
            </div>

            <div className="bg-blue-100/70 p-2 rounded-lg text-xs">
              <Bot className="w-3 h-3 inline mr-1 text-blue-600" />
              "I'd recommend Mailchimp for email marketing. It offers great templates, automation, and analytics. Would you like me to tell you more about their pricing?"
            </div>

            <div className="bg-white border rounded p-2 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-orange-100 rounded flex items-center justify-center">
                  <Building className="w-3 h-3 text-orange-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 text-xs">Mailchimp</h4>
                  <p className="text-gray-600 text-xs">Email marketing platform</p>
                </div>
                <Badge variant="secondary" className="text-xs px-1 py-0">Recommended</Badge>
              </div>
            </div>
          </div>

          <div className="mt-2 text-xs text-gray-500 flex items-center gap-1">
            <Sparkles className="w-2 h-2" />
            Powered by AdMesh
          </div>
        </div>
      )
    },
    {
      id: "dashboard",
      name: "Dashboard Integration",
      description: "Contextual recommendations within admin panels",
      icon: <Monitor className="w-5 h-5" />,
      preview: (
        <div className="bg-gray-50 border rounded-lg p-3 max-w-sm mx-auto">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center">
              <Monitor className="w-3 h-3 text-gray-600" />
            </div>
            <span className="font-medium text-gray-900 text-sm">Admin Dashboard</span>
          </div>

          <div className="space-y-2">
            <div className="bg-white border rounded p-2">
              <div className="text-xs text-gray-600 mb-1">Analytics Overview</div>
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-blue-50 p-1 rounded text-center">
                  <div className="text-xs font-medium">Users</div>
                  <div className="text-sm font-bold">1,234</div>
                </div>
                <div className="bg-green-50 p-1 rounded text-center">
                  <div className="text-xs font-medium">Revenue</div>
                  <div className="text-sm font-bold">$5.6K</div>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded p-2">
              <div className="flex items-center gap-1 mb-1">
                <Sparkles className="w-3 h-3 text-purple-600" />
                <span className="text-xs font-medium text-purple-700">Recommended Tools</span>
              </div>
              <div className="bg-white border rounded p-2">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-indigo-100 rounded flex items-center justify-center">
                    <Building className="w-2 h-2 text-indigo-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 text-xs">Google Analytics</h4>
                    <p className="text-gray-600 text-xs">Enhanced tracking features</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-2 text-xs text-gray-500 flex items-center gap-1">
            <Sparkles className="w-2 h-2" />
            Powered by AdMesh
          </div>
        </div>
      )
    }
  ];

  const selectedScenarioData = agentScenarios.find(scenario => scenario.id === selectedScenario);

  return (
    <div className="w-full max-w-lg mx-auto px-4 sm:px-0">
      <Card className="border border-gray-200 shadow-lg">
        <CardHeader className="text-center pb-4">
          <CardTitle className="flex items-center justify-center gap-2 text-lg">
            <Bot className="w-5 h-5 text-purple-600" />
            Seamless Agent Integration
          </CardTitle>
          <p className="text-gray-600 text-sm">
            See how AdMesh integrates naturally without compromising your UI
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Scenario Selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Choose Agent Type</label>
            <div className="grid grid-cols-1 gap-2">
              {agentScenarios.map((scenario) => (
                <div
                  key={scenario.id}
                  className={`border rounded-lg p-2 cursor-pointer transition-all ${
                    selectedScenario === scenario.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedScenario(scenario.id)}
                >
                  <div className="flex items-center gap-2">
                    <div className={`w-6 h-6 rounded flex items-center justify-center ${
                      selectedScenario === scenario.id ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {scenario.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 text-sm">{scenario.name}</h4>
                      <p className="text-gray-600 text-xs">{scenario.description}</p>
                    </div>
                    {selectedScenario === scenario.id && (
                      <CheckCircle className="w-4 h-4 text-purple-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Scenario Preview */}
          {selectedScenarioData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-3"
            >
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Live Preview</h4>
                {selectedScenarioData.preview}
              </div>

              {/* Benefits */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-green-700 mb-2">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">Why Agents Love AdMesh</span>
                </div>
                <ul className="text-green-600 text-xs space-y-1">
                  <li>• Maintains your agent's unique branding and personality</li>
                  <li>• Non-intrusive recommendations that enhance user experience</li>
                  <li>• Earn revenue while providing genuine value to users</li>
                  <li>• Seamless integration with any agent interface</li>
                </ul>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
