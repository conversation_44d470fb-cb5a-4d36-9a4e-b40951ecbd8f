"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  Bot,
  MessageSquare,
  Code,
  Zap,
  CheckCircle,
  ArrowRight,
  Copy,
  ExternalLink,
  Star,
  Building
} from "lucide-react";

interface IntegrationType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  codeSnippet: string;
  preview: React.ReactNode;
}

export default function InteractiveAgentDemo() {
  const [selectedIntegration, setSelectedIntegration] = useState<string>("sdk");
  const [showCode, setShowCode] = useState(false);
  const [copiedCode, setCopiedCode] = useState(false);

  const integrationTypes: IntegrationType[] = [
    {
      id: "sdk",
      name: "React SDK Integration",
      description: "Drop-in components for React applications",
      icon: <Code className="w-5 h-5" />,
      codeSnippet: `import { AdMeshConversationalUnit } from 'admesh-ui-sdk';

function ChatInterface() {
  const [recommendations, setRecommendations] = useState([]);
  
  const handleUserQuery = async (query) => {
    const response = await fetch('/api/admesh/recommend', {
      method: 'POST',
      body: JSON.stringify({ query })
    });
    const data = await response.json();
    setRecommendations(data.recommendations);
  };

  return (
    <div className="chat-container">
      {/* Your existing chat UI */}
      <ChatMessages />
      
      {/* AdMesh Integration */}
      <AdMeshConversationalUnit
        recommendations={recommendations}
        config={{
          displayMode: 'inline',
          context: 'chat',
          maxRecommendations: 3
        }}
        onRecommendationClick={(adId, link) => {
          // Track click and handle navigation
          window.open(link, '_blank');
        }}
      />
    </div>
  );
}`,
      preview: (
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="space-y-3">
            <div className="bg-blue-100 text-blue-800 p-3 rounded-lg text-sm">
              <strong>User:</strong> I need a good CRM for my startup
            </div>
            <div className="bg-white p-3 rounded-lg text-sm">
              <strong>AI Agent:</strong> For startups, I'd recommend looking at these CRM solutions:
              
              <div className="mt-3 space-y-2">
                <div className="bg-white border rounded-lg p-3 shadow-sm">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Building className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 text-sm">HubSpot CRM</h4>
                      <p className="text-gray-600 text-xs mt-1">Free CRM with powerful features for growing businesses</p>
                      <div className="flex items-center gap-2 mt-2">
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-xs text-gray-600">4.5</span>
                        </div>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-600">Free tier available</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: "api",
      name: "REST API Integration",
      description: "Direct API calls for custom implementations",
      icon: <Zap className="w-5 h-5" />,
      codeSnippet: `// Simple API integration
const getRecommendations = async (userQuery) => {
  try {
    const response = await fetch('https://api.useadmesh.com/v1/recommend', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: userQuery,
        context: 'chat',
        max_recommendations: 3,
        agent_id: 'your-agent-id'
      })
    });
    
    const data = await response.json();
    return data.recommendations;
  } catch (error) {
    console.error('AdMesh API error:', error);
    return [];
  }
};

// Usage in your chat handler
const handleChatMessage = async (message) => {
  // Process user message with your AI
  const aiResponse = await processWithAI(message);
  
  // Get relevant recommendations
  const recommendations = await getRecommendations(message);
  
  // Display both AI response and recommendations
  displayChatResponse(aiResponse, recommendations);
};`,
      preview: (
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="bg-gray-800 text-green-400 p-3 rounded-lg font-mono text-xs">
            <div className="mb-2">POST /v1/recommend</div>
            <div className="text-gray-300">
              {`{
  "query": "best project management tools",
  "context": "chat",
  "max_recommendations": 3
}`}
            </div>
            <div className="mt-3 text-blue-400">Response:</div>
            <div className="text-gray-300">
              {`{
  "recommendations": [
    {
      "title": "Asana",
      "reason": "Great for team collaboration",
      "intent_match_score": 0.92,
      "admesh_link": "https://...",
      "pricing": "Free tier available"
    }
  ]
}`}
            </div>
          </div>
        </div>
      )
    },
    {
      id: "webhook",
      name: "Webhook Integration",
      description: "Real-time recommendations via webhooks",
      icon: <MessageSquare className="w-5 h-5" />,
      codeSnippet: `// Webhook endpoint setup
app.post('/webhook/admesh', (req, res) => {
  const { recommendations, session_id, user_query } = req.body;
  
  // Process recommendations in real-time
  recommendations.forEach(rec => {
    // Add to your chat interface
    addRecommendationToChat(session_id, {
      type: 'recommendation',
      content: rec,
      timestamp: Date.now()
    });
  });
  
  res.status(200).json({ received: true });
});

// Configure webhook in AdMesh dashboard
const webhookConfig = {
  url: 'https://youragent.com/webhook/admesh',
  events: ['recommendation.generated'],
  filters: {
    min_intent_score: 0.7,
    categories: ['software', 'tools']
  }
};`,
      preview: (
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Real-time webhook active
            </div>
            <div className="bg-white border-l-4 border-green-500 p-3 rounded">
              <div className="text-xs text-gray-500 mb-1">Webhook received</div>
              <div className="text-sm">
                <strong>Event:</strong> recommendation.generated<br/>
                <strong>Session:</strong> chat_session_123<br/>
                <strong>Recommendations:</strong> 2 items matched
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const selectedIntegrationData = integrationTypes.find(type => type.id === selectedIntegration);

  const copyCode = () => {
    if (selectedIntegrationData) {
      navigator.clipboard.writeText(selectedIntegrationData.codeSnippet);
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto px-4 sm:px-0">
      <Card className="border-2 border-gray-200 shadow-lg">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-xl">
            <Bot className="w-6 h-6 text-purple-600" />
            Seamless Agent Integration
          </CardTitle>
          <p className="text-gray-600 text-sm">
            See how AdMesh integrates with your AI agent without compromising your UI
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Integration Type Selector */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-gray-700">Choose Integration Method</label>
            <div className="grid grid-cols-1 gap-3">
              {integrationTypes.map((type) => (
                <div
                  key={type.id}
                  className={`border rounded-lg p-3 cursor-pointer transition-all ${
                    selectedIntegration === type.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedIntegration(type.id)}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      selectedIntegration === type.id ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {type.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 text-sm">{type.name}</h4>
                      <p className="text-gray-600 text-xs">{type.description}</p>
                    </div>
                    {selectedIntegration === type.id && (
                      <CheckCircle className="w-5 h-5 text-purple-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Integration Preview */}
          {selectedIntegrationData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ArrowRight className="w-4 h-4 text-purple-600" />
                  <span className="text-sm font-medium text-gray-700">Live Preview</span>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowCode(!showCode)}
                    className="text-xs"
                  >
                    <Code className="w-3 h-3 mr-1" />
                    {showCode ? 'Hide Code' : 'View Code'}
                  </Button>
                </div>
              </div>

              {/* Code Snippet */}
              {showCode && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="bg-gray-900 text-gray-100 p-4 rounded-lg text-xs font-mono overflow-x-auto"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Integration Code</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyCode}
                      className="text-gray-400 hover:text-white h-6 px-2"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      {copiedCode ? 'Copied!' : 'Copy'}
                    </Button>
                  </div>
                  <pre className="whitespace-pre-wrap">{selectedIntegrationData.codeSnippet}</pre>
                </motion.div>
              )}

              {/* Visual Preview */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">How it looks in your agent:</h4>
                {selectedIntegrationData.preview}
              </div>

              {/* Benefits */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-green-700 mb-2">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">Integration Benefits</span>
                </div>
                <ul className="text-green-600 text-xs space-y-1">
                  <li>• Non-intrusive recommendations that enhance user experience</li>
                  <li>• Earn revenue from relevant product suggestions</li>
                  <li>• Maintain your agent's unique personality and branding</li>
                  <li>• Real-time tracking and analytics for optimization</li>
                </ul>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
